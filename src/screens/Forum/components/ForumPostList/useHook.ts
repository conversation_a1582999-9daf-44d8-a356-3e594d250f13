/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useCallback, useEffect, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectForumQuestions,
  selectForumQuestionsLoading,
  selectForumQuestionsRefreshing,
  selectForumQuestionsPagination,
  selectForumQuestionsFilters,
} from '@/src/redux/selectors/question';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import {
  fetchForumQuestions,
  setForumQuestionsFilters,
  clearForumQuestions,
  deleteQuestion,
  removeQuestionOptimistic,
} from '@/src/redux/slices/question/questionSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import type { QuestionDeleteOnePayloadI } from '@/src/networks/question/types';
import type { UseForumPostListResult } from './types';

const useForumPostList = (): UseForumPostListResult => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const questions = useSelector(selectForumQuestions);
  const loading = useSelector(selectForumQuestionsLoading);
  const refreshing = useSelector(selectForumQuestionsRefreshing);
  const pagination = useSelector(selectForumQuestionsPagination);
  const filters = useSelector(selectForumQuestionsFilters);
  const hasInitiallyFetched = useRef(false);

  useEffect(() => {
    if (!hasInitiallyFetched.current) {
      hasInitiallyFetched.current = true;

      const fetchLatestQuestions = async () => {
        try {
          await dispatch(
            fetchForumQuestions({
              refresh: false,
            }),
          ).unwrap();
        } catch (error) {
          if (!questions.length) {
            const errorMessage = `Failed to fetch questions: ${error instanceof APIResError ? error.message : 'Unknown error'
              }`;
            throw new Error(errorMessage);
          } else {
            showToast({ message: 'Failed to refresh questions', type: 'error' });
          }
        }
      };

      fetchLatestQuestions();
    }
  }, [dispatch]);

  const handleRefresh = async () => {
    try {
      await dispatch(
        fetchForumQuestions({
          refresh: true,
        }),
      ).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to refresh questions', type: 'error' });
    }
  };

  const handleLoadMore = async () => {
    if (loading || !pagination.hasMore) {
      return;
    }

    try {
      await dispatch(
        fetchForumQuestions({
          refresh: false,
        }),
      ).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to load more questions', type: 'error' });
    }
  };

  const toggleLiveMode = async () => {
    const newIsLive = !filters.isLive;
    dispatch(setForumQuestionsFilters({ isLive: newIsLive }));
    dispatch(clearForumQuestions());

    try {
      await dispatch(
        fetchForumQuestions({
          refresh: true,
        }),
      ).unwrap();
      showToast({
        message: newIsLive ? 'Live mode activated' : 'Live mode deactivated',
        type: 'success',
      });
    } catch (error) {
      showToast({
        message: 'Failed to update live mode',
        type: 'error',
      });
    }
  };

  const handleSelectPost = useCallback(
    async (post: { postId: string }) => {
      await dispatch(fetchForumQuestionDetail({ questionId: post.postId }));
      navigation.navigate('ForumAnswers', { postId: post.postId });
    },
    [dispatch, navigation],
  );

  const deleteForumQuestion = useCallback(
    async (questionId: string) => {
      try {
        const payload: QuestionDeleteOnePayloadI = { questionId };
        await dispatch(deleteQuestion(payload)).unwrap();
        dispatch(removeQuestionOptimistic({ questionId }));
        showToast({
          message: 'Question deleted successfully',
          type: 'success',
        });
        navigation.goBack();
      } catch (error) {
        showToast({
          message: 'Failed to delete question',
          type: 'error',
          description: error instanceof APIResError ? error.message : 'Unknown error',
        });
      }
    },
    [dispatch],
  );

  return {
    questions,
    loading,
    refreshing,
    hasMore: pagination.hasMore,
    isLive: filters.isLive,
    handleRefresh,
    handleLoadMore,
    toggleLiveMode,
    handleSelectPost,
    deleteForumQuestion,
  };
};

export default useForumPostList;
